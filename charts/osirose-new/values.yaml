
repository: gitea.azgstudio.com/raven

autoscaling:
  enabled: false

global:
  env:
    LOG_LEVEL: "info"
    APP_ENV: "dev"
    DATABASE_URL: "" # This is a placeholder. Will be dynamically constructed
    REDIS_URL: "redis://valkey:6379/0"
    LISTEN_ADDR: "0.0.0.0"

services:
  - name: auth-service
    replicas: 1
    serviceAccount: azgstudio-serviceaccount
    image: auth-service:latest
    port: 50051
    env:
      SERVICE_PORT: 50051
    service:
      portName: auth-service
      port: 50051
      targetPort: 50051
      protocol: TCP

  - name: chat-service
    replicas: 1
    serviceAccount: azgstudio-serviceaccount
    image: chat-service:latest
    port: 50055
    env:
      SERVICE_PORT: 50055
      LOG_LEVEL: "debug"
    service:
      portName: chat-service
      port: 50055
      targetPort: 50055
      protocol: TCP

  - name: character-service
    replicas: 1
    serviceAccount: azgstudio-serviceaccount
    image: character-service:latest
    port: 50053
    env:
      SERVICE_PORT: 50053
    service:
      annotations:
        name: "AZG Studio"
      portName: character-service
      port: 50053
      targetPort: 50053
      protocol: TCP

  - name: database-service
    replicas: 1
    serviceAccount: azgstudio-serviceaccount
    image: database-service:latest
    port: 50052
    env:
      SERVICE_PORT: 50052
    service:
      portName: database-service
      port: 50052
      targetPort: 50052
      protocol: TCP

  - name: packet-service
    replicas: 1
    serviceAccount: azgstudio-serviceaccount
    image: packet-service:latest
    port: 29000
    env:
      SERVICE_PORT: 29000
      LOG_LEVEL: "debug"
    service:
      type: LoadBalancer
      portName: game-packet-service
      port: 29000
      targetPort: 29000
      protocol: TCP

  - name: world-service
    replicas: 1
    serviceAccount: azgstudio-serviceaccount
    image: world-service:latest
    port: 50054
    env:
      SERVICE_PORT: 50054
      LOG_LEVEL: "debug"
      LOGIC_LOG_LEVEL: "debug"
#      MAP_IDS: "1,2,3,4,5,6,8,9,10,11,15,16,17,18,20,21,22,23,24,25,26,27,28,29,31,32,33,34,35,36,37,40,42,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,70,71,72,73,74,75,76,77,78,79,80"
      MAP_IDS: "1,2,3,4,5,6,8,9,10,11,15,16,17,18,20"
      WORLD_SERVICE_NAME: "Athena"
    service:
      annotations:
        name: "Athena"
      portName: world-service
      port: 50054
      targetPort: 50054
      protocol: TCP

tests:
  enabled: false
  services:
    - name: api-service
      testCommand: ["curl", "-f", "http://api-service:8080/health"]
      image: curlimages/curl:latest
